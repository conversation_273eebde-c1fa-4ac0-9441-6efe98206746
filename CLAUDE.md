# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a Webflow design project for implementing responsive expandable pricing cards. The project adds accordion-style functionality to pricing tables that appears only on tablet (768-991px) and mobile (<768px) breakpoints while preserving existing desktop table functionality.

## Project Structure

- **Design Assets**: Visual references for implementation
  - `Figma Pricing New Design.png`: Main design reference
  - `tablet/`: Tablet-specific design mockups showing expandable states, containers, frames, and line separators

- **Implementation Guides**: 
  - `build-instructions-webflow-gpt.md`: Staging deployment guide with environment detection patterns
  - `build-instructions-augment.md`: Comprehensive Stage 1 implementation guide (400+ lines) with detailed Webflow Designer instructions, element hierarchy, class naming, and interaction setup

## Architecture & Implementation Strategy

### Element Hierarchy (from build-instructions-augment.md)

Each pricing card gets this new structure:
```
Pricing Card (existing)
└── Expandable Section Container (pricing-expandable-section)
    ├── Expand Trigger <PERSON> (pricing-expand-trigger)
    │   ├── Trigger Text ("View Details")
    │   └── Chevron Icon (pricing-chevron-icon)
    └── Expandable Content Panel (pricing-expandable-content)
        └── Content List (pricing-content-list)
            └── Feature Items (pricing-feature-item)
```

### Class Naming Architecture

**Primary Structure Classes:**
- `pricing-expandable-section`: Main container (hidden desktop ≥992px, visible tablet/mobile)
- `pricing-expand-trigger`: Clickable button element
- `pricing-trigger-content`: Flex container for button layout
- `pricing-expandable-content`: Animated content panel using max-height transitions

**State Management:**
- `is-expanded`/`is-collapsed`: Interaction states for accordion behavior
- `is-staging`: Environment detection flag from domain guards

### Responsive Breakpoint Strategy

- **Desktop (≥992px)**: `pricing-expandable-section` display: none (preserves existing table)
- **Tablet (768-991px)**: `pricing-expandable-section` display: block with full functionality
- **Mobile (<768px)**: Same as tablet with touch-optimized interactions

### Webflow IX2 Animation Pattern

**Expand Sequence:**
1. Max-height: 0px → Auto (300ms, ease-out)
2. Opacity: 0 → 1 (200ms, 100ms delay, ease-out)  
3. Chevron rotate: 0deg → 180deg (200ms, ease-out)

**Collapse Sequence:**
1. Opacity: 1 → 0 (150ms, ease-in)
2. Max-height: Auto → 0px (250ms, 50ms delay, ease-in)
3. Chevron rotate: 180deg → 0deg (200ms, ease-in)

### Staging Safety Implementation

**Domain Detection (build-instructions-webflow-gpt.md):**
```javascript
var isStaging = \.webflow\.io$.test(host) || host === 'staging.intruder.io';
if (isStaging) document.documentElement.classList.add('is-staging');
```

**CSS Gating:**
```css
.pricing-expandable-section { display: none; }
@media (max-width: 991px) {
  html.is-staging .pricing-expandable-section { display: block; }
}
```

### Webflow Designer Implementation Workflow

1. **Structure Creation**: Build expandable section hierarchy within existing pricing cards
2. **Class Application**: Apply systematic `pricing-*` naming convention
3. **Responsive Configuration**: Set breakpoint-specific display properties
4. **Visual Styling**: Apply consistent design system styling
5. **IX2 Setup**: Configure expand/collapse interactions with proper targeting
6. **Replication**: Copy structure across all pricing cards with interaction linking
7. **Testing**: Validate across Desktop/Tablet/Mobile and multiple browsers

### Testing Requirements

**Breakpoint Validation:**
- Desktop: Complete hiding of expandable sections, existing functionality preserved
- Tablet/Mobile: Full expand/collapse functionality active
- Cross-browser: Chrome, Safari, Firefox, Edge compatibility

**Interaction Testing:**
- Single card expand/collapse
- Multiple simultaneous expansions
- Smooth animations without JavaScript errors
- Touch responsiveness on mobile devices

This is a **visual design implementation project** using Webflow Designer. No traditional development commands (build, test, lint) apply. Implementation occurs through Webflow's visual interface following the detailed guides provided.