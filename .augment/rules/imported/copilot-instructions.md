---
type: "manual"
---

# Copilot AI Agent Instructions

This project enforces strict functional programming and pragmatic engineering principles. AI agents must follow these rules to ensure code quality, maintainability, and alignment with team practices. See `rules.md` for detailed rationale and examples.

## 1. Core Principles
- **KISS, DRY, YAGNI:** Always prefer simple, direct solutions. Avoid unnecessary abstractions, optimizations, or code not required for the current task.
- **Single Responsibility:** Each function/module must do one thing only.
- **Separation of Concerns:** Keep UI, data, and state logic separate.
- **Atomic Changes:** Only address the user's explicit request—no unrelated refactoring.

## 2. Functional Programming
- **No Classes:** Use pure functions, closures, and higher-order functions. Classes only if required by external APIs.
- **Immutability:** Never mutate data. Use spread, `map`, `filter`, etc. for updates.
- **Statelessness:** No reliance on global or shared mutable state.
- **Idempotence:** Functions must yield the same result for the same input, with no side effects.

## 3. Code Style & TypeScript
- **Behavioral Naming:** Name by what code does, not type. Use clear verbs for functions.
- **Use `const` and Arrow Functions:** Default to `const` and arrow functions. Use `async/await` for async code.
- **No `any`:** Use `unknown` if type is unclear, with explicit checks.
- **Simple Types:** Avoid complex generics or recursive types.

## 4. Error Handling
- **Fail Fast:** Validate inputs early, throw explicit errors.
- **No Silent Failures:** Always surface errors; never swallow exceptions.
- **Graceful Degradation:** Provide fallbacks where possible.

## 5. AI Agent Behavior
- **Present Options:** If multiple valid solutions exist, explain trade-offs.
- **Incremental Delivery:** Implement the minimal solution first, then ask before adding enhancements.
- **Reference Patterns:** Follow existing patterns in `rules.md` and the codebase.
- **Justify Decisions:** Briefly explain reasoning before code.

## 6. Testing (Playwright)
- **Organize by User Journey:** Test files mirror user flows.
- **Use Fixtures & Reusable Actions:** Leverage Playwright fixtures and pure helper functions.
- **Visual Regression:** Integrate Percy.io for snapshots; handle dynamic content in tests.
- **CI/CD:** Tests must run in Docker and GitLab CI; use env vars for config.

## 7. Project Structure
- **Feature-Based Folders:** Group by feature/domain, not file type.
- **Minimal Dependencies:** Add new libraries only if essential and well-vetted.

## 8. Documentation & Maintenance
- **Inline Docs:** Document public functions with intent, not mechanics.
- **Update Imports:** When renaming, update all imports.

---

**Reference:** All detailed rules, code examples, and rationale are in [`rules.md`](../rules.md). Always consult this file for clarification or when in doubt.
